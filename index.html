<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Korku Kaçış Oyunu</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #000; color: #fff; font-family: 'Courier New', Courier, monospace; }
        canvas { display: block; }
        #main-canvas { position: absolute; top: 0; left: 0; z-index: 1; }
        #ui-container { position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center; z-index: 3; }
        #crosshair { width: 10px; height: 10px; border: 1px solid white; border-radius: 50%; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); display: none; }
        .message { font-size: 2em; padding: 20px; background-color: rgba(0, 0, 0, 0.5); border-radius: 10px; display: none; }
        #start-screen, #game-over-screen { pointer-events: auto; cursor: pointer; background-color: rgba(0, 0, 0, 0.8); z-index: 10; }
        #start-screen h1, #game-over-screen h1 { font-size: 3em; margin-bottom: 20px; }
        #start-screen p, #game-over-screen p { font-size: 1.2em; }
        .ui-bar-container {
            position: absolute;
            bottom: 20px;
            width: 200px;
            height: 15px;
            border: 2px solid rgba(255, 255, 255, 0.5);
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 5px;
            display: none;
        }
        #stamina-container { left: 20px; }
        #battery-container { right: 20px; }
        .ui-bar {
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            width: 100%;
            transition: width 0.1s linear;
            border-radius: 3px;
        }
        #spare-batteries-ui {
            position: absolute;
            bottom: 45px;
            right: 20px;
            font-size: 1.5em;
            text-shadow: 2px 2px 4px #000;
            display: none;
        }
        #whisper-message {
            position: absolute;
            bottom: 80px;
            font-size: 1.5em;
            color: #ff4444;
            text-shadow: 1px 1px 5px #000;
            opacity: 0;
            transition: opacity 0.5s;
        }
        #minimap-container {
            position: absolute;
            top: 20px;
            right: 20px;
            border: 2px solid rgba(255, 255, 255, 0.5);
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            z-index: 2;
        }
        #minimap-canvas {
            display: block;
        }
        #hiding-overlay {
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
            background: radial-gradient(ellipse at center, rgba(0,0,0,0) 0%, rgba(0,0,0,0.8) 30%, rgba(0,0,0,0.98) 60%, #000000 100%);
            z-index: 5;
            display: none;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 1.5em;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="ui-container">
        <div id="start-screen" class="message" style="display: flex; flex-direction: column;">
            <h1>Gözcüler</h1>
            <p>Bir sonraki seviyeye geçmek için anahtarı bul.</p>
            <p>Sadece ışıktan korkarlar.</p>
            <p style="font-size: 0.8em; color: #aaa;">✨ Stabil Düşman Modeli</p>
            <br>
            <p><strong>Başlamak için Tıkla</strong></p>
            <p>(F: Fener | R: Batarya | E: Etkileşim/Saklan)</p>
        </div>
        <div id="game-over-screen" class="message">
            <h1>Oyun Bitti</h1>
            <p id="game-over-taunt">Kaçamadın.</p>
            <p style="font-size: 0.8em;">Yeniden Başlamak için Tıkla</p>
        </div>
        <div id="key-message" class="message" style="position: absolute; top: 10%; display: none;">Anahtar Bulundu!</div>
        <div id="door-locked-message" class="message" style="position: absolute; top: 10%; display: none;">Kapı Kilitli. Anahtarı bulmalısın.</div>
        <div id="crosshair"></div>
        <div id="stamina-container" class="ui-bar-container">
            <div id="stamina-bar" class="ui-bar"></div>
        </div>
        <div id="battery-container" class="ui-bar-container">
            <div id="battery-bar" class="ui-bar"></div>
        </div>
        <div id="spare-batteries-ui">🔋 0</div>
        <div id="whisper-message"></div>
    </div>
    <div id="minimap-container">
        <canvas id="minimap-canvas"></canvas>
    </div>
    <div id="hiding-overlay">Nefesini tut... (Çıkmak için E'ye bas)</div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.7.77/Tone.js"></script>
    <!-- FIX: Removed GLTFLoader as it was causing issues -->
    <script>
        // --- SCENE SETUP ---
        let scene, camera, renderer, ambientLight;
        let player, flashlight;
        const clock = new THREE.Clock();
        const raycaster = new THREE.Raycaster();
        const textureLoader = new THREE.TextureLoader();

        // --- MINIMAP ---
        let minimapCanvas, minimapCtx;
        let visitedMap;
        const minimapCellSize = 8;

        // --- AUDIO SETUP ---
        let sounds = {}; let audioInitialized = false;

        // --- GAME STATE ---
        let level = 1; let hasKey = false; let gameActive = false; let isGameOver = false;
        const collidableObjects = []; let collectibles = []; let interactables = []; let key, door;
        let enemies = []; let mazeMap; const wallSize = 2.5; const wallHeight = 4;

        // --- PLAYER & FLASHLIGHT ---
        const moveState = { forward: false, backward: false, left: false, right: false, sprint: false };
        const playerVelocity = new THREE.Vector3(); let lastMoveDirection = new THREE.Vector3();
        const playerSpeed = 3.0; const sprintMultiplier = 1.8; const playerHeight = 1.8;
        const playerCollider = new THREE.Box3(); const playerSize = new THREE.Vector3(0.7, playerHeight, 0.7);
        let bobTime = 0; let footstepCooldown = 0;
        let isFlashlightOn = true;
        let isHiding = false;
        
        // --- STAMINA & BATTERY ---
        let stamina = 100; const maxStamina = 100; const staminaDrainRate = 35;
        const staminaRegenRate = 15; const staminaRegenDelay = 2; let staminaRegenCooldown = 0;
        let flashlightBattery = 100; const maxFlashlightBattery = 100;
        const flashlightDrainRate = 3.5;
        let spareBatteries = 0; const baseFlashlightIntensity = 2;

        // --- GEMINI ---
        let whisperCooldown = 15.0; 

        function init() {
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x000000);
            scene.fog = new THREE.Fog(0x000000, 2, 15);
            ambientLight = new THREE.AmbientLight(0x101015); scene.add(ambientLight);
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.y = 0.65;
            player = new THREE.Object3D(); player.add(camera); scene.add(player);
            flashlight = new THREE.SpotLight(0xffcc88, baseFlashlightIntensity, 18, Math.PI / 5, 0.5, 1.5);
            flashlight.castShadow = true; camera.add(flashlight);
            flashlight.position.set(0, 0, 0.1); flashlight.target = camera;
            renderer = new THREE.WebGLRenderer(); renderer.domElement.id = 'main-canvas';
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true; renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.body.appendChild(renderer.domElement);

            minimapCanvas = document.getElementById('minimap-canvas');
            minimapCtx = minimapCanvas.getContext('2d');

            setupEventListeners();
            loadLevel(level);
            animate();
        }
        
        function initAudio() {
            if (audioInitialized) return;
            Tone.start(); Tone.Destination.volume.value = 5; 
            sounds.footstep = new Tone.MembraneSynth({ pitchDecay: 0.01, octaves: 8, envelope: { attack: 0.001, decay: 0.2, sustain: 0 }, volume: -8 }).toDestination();
            sounds.enemyFootstep = new Tone.MembraneSynth({ pitchDecay: 0.04, octaves: 3, envelope: { attack: 0.01, decay: 0.4, sustain: 0 }, volume: 0 }).toDestination();
            sounds.keyPickup = new Tone.Synth({ envelope: { attack: 0.01, decay: 0.2, release: 0.2}, volume: -5 }).toDestination();
            sounds.batteryPickup = new Tone.Synth({ oscillator: {type: 'square'}, envelope: { attack: 0.01, decay: 0.1, release: 0.1}, volume: -10 }).toDestination();
            sounds.reload = new Tone.NoiseSynth({ noise: { type: 'white' }, envelope: { attack: 0.01, decay: 0.15, sustain: 0}, volume: -15 }).toDestination();
            sounds.reloadFail = new Tone.MembraneSynth({ pitchDecay: 0.05, octaves: 1, envelope: { attack: 0.001, decay: 0.15, sustain: 0 }, volume: -15 }).toDestination();
            sounds.doorLocked = new Tone.MembraneSynth({ pitchDecay: 0.1, octaves: 2, envelope: { attack: 0.01, decay: 0.5, sustain: 0 }, volume: -10 }).toDestination();
            sounds.gameOver = new Tone.NoiseSynth({ noise: { type: 'pink' }, envelope: { attack: 0.01, decay: 1.5, sustain: 0.1, release: 1 }, volume: -5 }).toDestination();
            sounds.ambience = new Tone.NoiseSynth({ noise: { type: 'brown' }, envelope: { attack: 5, decay: 2, sustain: 1 }, volume: -30 }).toDestination();
            sounds.ambience.triggerAttack();
            sounds.hide = new Tone.MembraneSynth({pitchDecay: 0.2, octaves: 2, envelope: {attack: 0.01, decay: 0.3, sustain: 0}, volume: -12}).toDestination();
            sounds.unhide = new Tone.MembraneSynth({pitchDecay: 0.2, octaves: 3, envelope: {attack: 0.01, decay: 0.2, sustain: 0}, volume: -12}).toDestination();
            const musicSynth = new Tone.FMSynth({ harmonicity: 3.01, modulationIndex: 14, envelope: { attack: 0.2, decay: 1 }, modulation: { type: "sine" }, modulationEnvelope: { attack: 0.2, decay: 1 }, volume: -28 }).toDestination();
            const musicPattern = new Tone.Sequence((time, note) => { musicSynth.triggerAttackRelease(note, "1n", time); }, ["C2", ["Eb2", "D2"], "G1", null], "1n");
            musicPattern.start(0); musicPattern.loop = true; Tone.Transport.start();
            audioInitialized = true;
        }

        function generateMaze(width, height) {
            const map = Array(height).fill(null).map(() => Array(width).fill(1));
            const stack = []; const startX = 1, startY = 1;
            map[startY][startX] = 0; stack.push([startX, startY]);
            while (stack.length > 0) {
                const [cx, cy] = stack[stack.length - 1]; const neighbors = [];
                if (cx > 1 && map[cy][cx - 2] === 1) neighbors.push([cx - 2, cy, cx - 1, cy]);
                if (cx < width - 2 && map[cy][cx + 2] === 1) neighbors.push([cx + 2, cy, cx + 1, cy]);
                if (cy > 1 && map[cy - 2][cx] === 1) neighbors.push([cx, cy - 2, cx, cy - 1]);
                if (cy < height - 2 && map[cy + 2][cx] === 1) neighbors.push([cx, cy + 2, cx, cy + 1]);
                if (neighbors.length > 0) {
                    const [nx, ny, px, py] = neighbors[Math.floor(Math.random() * neighbors.length)];
                    map[ny][nx] = 0; map[py][px] = 0; stack.push([nx, ny]);
                } else { stack.pop(); }
            } return map;
        }

        // FIX: Reverted to simple geometric model for stability
        function createEnemyModel() {
            const group = new THREE.Group();
            const material = new THREE.MeshStandardMaterial({ color: 0x555555, roughness: 0.8, metalness: 0.1 });
            const head = new THREE.Mesh(new THREE.SphereGeometry(0.3, 16, 12), material);
            head.position.y = 0.6;
            const torso = new THREE.Mesh(new THREE.CylinderGeometry(0.3, 0.4, 1.2, 8), material);
            torso.position.y = -0.2;
            const base = new THREE.Mesh(new THREE.CylinderGeometry(0.5, 0.5, 0.2, 12), material);
            base.position.y = -0.9;
            group.add(head, torso, base);
            group.children.forEach(c => { c.castShadow = true; c.receiveShadow = true; });
            return group;
        }

        function loadLevel(levelNum) {
            while (collidableObjects.length) scene.remove(collidableObjects.pop());
            while (collectibles.length) scene.remove(collectibles.pop());
            while (interactables.length) scene.remove(interactables.pop());
            enemies.forEach(e => scene.remove(e.model));
            enemies = [];
            if (key) scene.remove(key); if (door) scene.remove(door);
            hasKey = false; stamina = maxStamina;
            flashlightBattery = maxFlashlightBattery; spareBatteries = 0;

            const mazeWidth = 13 + (levelNum * 2); const mazeHeight = 13 + (levelNum * 2);
            mazeMap = generateMaze(mazeWidth, mazeHeight);
            visitedMap = Array(mazeHeight).fill(null).map(() => Array(mazeWidth).fill(false));
            
            minimapCanvas.width = mazeWidth * minimapCellSize;
            minimapCanvas.height = mazeHeight * minimapCellSize;

            const emptySpaces = [];
            const deadEnds = [];

            for (let i = 0; i < mazeHeight; i++) {
                for (let j = 0; j < mazeWidth; j++) {
                    if (mazeMap[i][j] === 0) {
                        emptySpaces.push({ x: j, z: i });
                        let neighborWalls = 0;
                        if(i > 0 && mazeMap[i-1][j] === 1) neighborWalls++;
                        if(i < mazeHeight - 1 && mazeMap[i+1][j] === 1) neighborWalls++;
                        if(j > 0 && mazeMap[i][j-1] === 1) neighborWalls++;
                        if(j < mazeWidth - 1 && mazeMap[i][j+1] === 1) neighborWalls++;
                        if(neighborWalls >= 3) deadEnds.push({ x: j, z: i });
                    }
                }
            }

            const wallTexture = textureLoader.load('https://raw.githubusercontent.com/mrdoob/three.js/master/examples/textures/brick_diffuse.jpg');
            wallTexture.wrapS = THREE.RepeatWrapping; wallTexture.wrapT = THREE.RepeatWrapping;
            wallTexture.repeat.set(1, 1);
            const wallMaterial = new THREE.MeshStandardMaterial({ map: wallTexture, roughness: 0.9 });
            
            const floorTexture = textureLoader.load('https://raw.githubusercontent.com/mrdoob/three.js/master/examples/textures/hardwood2_diffuse.jpg');
            floorTexture.wrapS = THREE.RepeatWrapping; floorTexture.wrapT = THREE.RepeatWrapping;
            floorTexture.repeat.set(mazeWidth/2, mazeHeight/2);
            const floorMaterial = new THREE.MeshStandardMaterial({ map: floorTexture, roughness: 0.8 });

            for (let i = 0; i < mazeHeight; i++) {
                for (let j = 0; j < mazeWidth; j++) {
                    if (mazeMap[i][j] === 1) {
                        const wallGeometry = new THREE.BoxGeometry(wallSize, wallHeight, wallSize);
                        const wall = new THREE.Mesh(wallGeometry, wallMaterial);
                        wall.position.set((j - mazeWidth / 2) * wallSize, wallHeight / 2, (i - mazeHeight / 2) * wallSize);
                        wall.castShadow = true; wall.receiveShadow = true; scene.add(wall); collidableObjects.push(wall);
                    }
                }
            }
            
            const floor = new THREE.Mesh(new THREE.PlaneGeometry(mazeWidth * wallSize, mazeHeight * wallSize), floorMaterial);
            floor.rotation.x = -Math.PI / 2; floor.receiveShadow = true; scene.add(floor);

            const playerStartIndex = Math.floor(Math.random() * emptySpaces.length / 4);
            const playerStartPos = gridToWorld(emptySpaces[playerStartIndex].x, emptySpaces[playerStartIndex].z, mazeWidth, mazeHeight);
            player.position.set(playerStartPos.x, playerHeight / 2, playerStartPos.z); emptySpaces.splice(playerStartIndex, 1);

            mazeMap[mazeHeight - 2][mazeWidth - 2] = 2;
            const doorPos = gridToWorld(mazeWidth - 2, mazeHeight - 2, mazeWidth, mazeHeight);
            const doorTexture = textureLoader.load('https://raw.githubusercontent.com/mrdoob/three.js/master/examples/textures/door.jpg');
            const doorMaterial = new THREE.MeshStandardMaterial({ map: doorTexture });
            door = new THREE.Mesh(new THREE.BoxGeometry(wallSize, 3, 0.2), doorMaterial); 
            door.position.set(doorPos.x, 1.5, doorPos.z);
            scene.add(door); collidableObjects.push(door);
            
            const keyIndex = emptySpaces.length - 1 - Math.floor(Math.random() * emptySpaces.length / 4);
            const keyPos = gridToWorld(emptySpaces[keyIndex].x, emptySpaces[keyIndex].z, mazeWidth, mazeHeight);
            const keyMaterial = new THREE.MeshStandardMaterial({ color: 0xffd700, metalness: 1, roughness: 0.2, emissive: 0xffd700, emissiveIntensity: 0.5 });
            key = new THREE.Mesh(new THREE.BoxGeometry(0.1, 0.3, 0.1), keyMaterial); 
            key.position.set(keyPos.x, 0.5, keyPos.z); key.name = "key";
            scene.add(key); collectibles.push(key); emptySpaces.splice(keyIndex, 1);

            for(let i=0; i<3; i++) {
                if(emptySpaces.length === 0) break;
                const battIndex = Math.floor(Math.random() * emptySpaces.length);
                const battPos = gridToWorld(emptySpaces[battIndex].x, emptySpaces[battIndex].z, mazeWidth, mazeHeight);
                const batteryMaterial = new THREE.MeshStandardMaterial({ color: 0x33ff55, metalness: 0.5, roughness: 0.3 });
                const battery = new THREE.Mesh(new THREE.CylinderGeometry(0.1, 0.1, 0.25, 8), batteryMaterial);
                battery.position.set(battPos.x, 0.125, battPos.z);
                battery.rotation.z = Math.PI / 2; battery.name = "battery";
                scene.add(battery); collectibles.push(battery);
                emptySpaces.splice(battIndex, 1);
            }

            const closetMaterial = new THREE.MeshStandardMaterial({ color: 0x403020 });
            for (const pos of deadEnds) {
                if (Math.random() < 0.3) {
                    const closetPos = gridToWorld(pos.x, pos.z, mazeWidth, mazeHeight);
                    const closet = new THREE.Mesh(new THREE.BoxGeometry(1.5, 2.5, 1), closetMaterial);
                    closet.position.set(closetPos.x, 1.25, closetPos.z);
                    closet.name = "closet";
                    scene.add(closet);
                    interactables.push(closet);
                }
            }

            const numEnemies = 1;
            const baseEnemySpeed = 3.5;
            const enemySpeed = baseEnemySpeed + (levelNum - 1) * 0.4;

            for (let i = 0; i < numEnemies; i++) {
                if(emptySpaces.length === 0) break;
                const enemyIndex = Math.floor(Math.random() * emptySpaces.length);
                const enemyStartPos = gridToWorld(emptySpaces[enemyIndex].x, emptySpaces[enemyIndex].z, mazeWidth, mazeHeight);
                
                const enemyModel = createEnemyModel();
                enemyModel.position.set(enemyStartPos.x, 1.0, enemyStartPos.z); // Adjusted Y
                
                const enemyObject = {
                    model: enemyModel,
                    speed: enemySpeed,
                    path: [],
                    pathCooldown: 0,
                    footstepCooldown: Math.random() * 0.5 + 0.5,
                    aiState: 'PATROL',
                };
                scene.add(enemyModel);
                enemies.push(enemyObject);
                emptySpaces.splice(enemyIndex, 1);
            }
        }
        
        function gridToWorld(x, z, w, h) { return { x: (x - w / 2) * wallSize, z: (z - h / 2) * wallSize }; }
        function worldToGrid(x, z, w, h) { return { x: Math.round(x / wallSize + w / 2), z: Math.round(z / wallSize + h / 2) }; }

        function findPath(startPos, endPos) {
            const openList = []; const closedList = []; const grid = mazeMap;
            if(!grid[endPos.z] || grid[endPos.z][endPos.x] === 1) return [];
            const startNode = { x: startPos.x, z: startPos.z, g: 0, h: 0, f: 0, parent: null };
            const endNode = { x: endPos.x, z: endPos.z }; openList.push(startNode);
            let maxIterations = grid.length * grid[0].length;
            while (openList.length > 0 && maxIterations > 0) {
                maxIterations--; let lowestFIndex = 0;
                for (let i = 1; i < openList.length; i++) { if (openList[i].f < openList[lowestFIndex].f) lowestFIndex = i; }
                const currentNode = openList.splice(lowestFIndex, 1)[0]; closedList.push(currentNode);
                if (currentNode.x === endNode.x && currentNode.z === endNode.z) {
                    let path = []; let current = currentNode; while (current) { path.push({ x: current.x, z: current.z }); current = current.parent; } return path.reverse();
                }
                const neighbors = []; const { x, z } = currentNode;
                if (grid[z - 1] && grid[z - 1][x] !== 1) neighbors.push({ x, z: z - 1 });
                if (grid[z + 1] && grid[z + 1][x] !== 1) neighbors.push({ x, z: z + 1 });
                if (grid[z] && grid[z][x - 1] !== 1) neighbors.push({ x: x - 1, z });
                if (grid[z] && grid[z][x + 1] !== 1) neighbors.push({ x: x + 1, z });
                for (const neighbor of neighbors) {
                    if (closedList.find(n => n.x === neighbor.x && n.z === neighbor.z)) continue;
                    const gScore = currentNode.g + 1; let gScoreIsBest = false;
                    let existingNode = openList.find(n => n.x === neighbor.x && n.z === neighbor.z);
                    if (!existingNode) { gScoreIsBest = true; neighbor.h = Math.abs(neighbor.x - endNode.x) + Math.abs(neighbor.z - endNode.z); openList.push(neighbor); } 
                    else if (gScore < existingNode.g) { gScoreIsBest = true; }
                    if (gScoreIsBest) { neighbor.parent = currentNode; neighbor.g = gScore; neighbor.f = neighbor.g + neighbor.h; }
                }
            } return [];
        }
        
        function setupEventListeners() {
            window.addEventListener('resize', onWindowResize, false); camera.rotation.order = 'YXZ'; 
            const startScreen = document.getElementById('start-screen');
            const gameOverScreen = document.getElementById('game-over-screen');
            const crosshair = document.getElementById('crosshair');
            const staminaContainer = document.getElementById('stamina-container');
            const batteryContainer = document.getElementById('battery-container');
            const spareBatteriesUI = document.getElementById('spare-batteries-ui');
            const minimapContainer = document.getElementById('minimap-container');
            const hidingOverlay = document.getElementById('hiding-overlay');

            startScreen.addEventListener('click', () => { initAudio(); document.body.requestPointerLock(); });
            gameOverScreen.addEventListener('click', () => { isGameOver = false; level = 1; loadLevel(level); document.body.requestPointerLock(); });
            document.addEventListener('pointerlockchange', () => {
                gameActive = document.pointerLockElement === document.body;
                crosshair.style.display = gameActive ? 'block' : 'none';
                staminaContainer.style.display = gameActive ? 'block' : 'none';
                batteryContainer.style.display = gameActive ? 'block' : 'none';
                spareBatteriesUI.style.display = gameActive ? 'block' : 'none';
                minimapContainer.style.display = gameActive ? 'block' : 'none';
                hidingOverlay.style.display = isHiding ? 'flex' : 'none';
                if (!gameActive) {
                    if (isGameOver) { gameOverScreen.style.display = 'flex'; gameOverScreen.style.flexDirection = 'column'; } 
                    else { startScreen.style.display = 'flex'; startScreen.style.flexDirection = 'column'; }
                } else { startScreen.style.display = 'none'; gameOverScreen.style.display = 'none'; }
            }, false);
            document.addEventListener('mousemove', (event) => {
                if (!gameActive || isHiding) return;
                player.rotation.y -= event.movementX * 0.002;
                camera.rotation.x -= event.movementY * 0.002;
                camera.rotation.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, camera.rotation.x));
            });
            document.addEventListener('keydown', (event) => {
                if (event.code === 'KeyE' && isHiding) { interact(); return; }
                if (!gameActive || isHiding) return;
                switch(event.code) {
                    case 'KeyW': moveState.forward = true; break; case 'KeyS': moveState.backward = true; break;
                    case 'KeyA': moveState.left = true; break; case 'KeyD': moveState.right = true; break;
                    case 'ShiftLeft': moveState.sprint = true; break; case 'ShiftRight': moveState.sprint = true; break;
                    case 'KeyF': isFlashlightOn = !isFlashlightOn; flashlight.visible = isFlashlightOn; break;
                    case 'KeyR': 
                        if (spareBatteries > 0 && flashlightBattery < maxFlashlightBattery) {
                            spareBatteries--; flashlightBattery = maxFlashlightBattery; if (audioInitialized) sounds.reload.triggerAttackRelease("4n");
                        } else { if (audioInitialized) sounds.reloadFail.triggerAttackRelease("C2", "16n"); }
                        break;
                    case 'KeyE': interact(); break;
                }
            });
            document.addEventListener('keyup', (event) => {
                 switch(event.code) {
                    case 'KeyW': moveState.forward = false; break; case 'KeyS': moveState.backward = false; break;
                    case 'KeyA': moveState.left = false; break; case 'KeyD': moveState.right = false; break;
                    case 'ShiftLeft': moveState.sprint = false; break; case 'ShiftRight': moveState.sprint = false; break;
                }
            });
        }

        function onWindowResize() { camera.aspect = window.innerWidth / window.innerHeight; camera.updateProjectionMatrix(); renderer.setSize(window.innerWidth, window.innerHeight); }
        
        function interact() {
            if (isHiding) {
                isHiding = false;
                if(audioInitialized) sounds.unhide.triggerAttackRelease("D2", "8n");
                document.getElementById('hiding-overlay').style.display = 'none';
                return;
            }

            for (const item of interactables) {
                if (player.position.distanceTo(item.position) < 2.0) {
                    if (item.name === "closet") {
                        isHiding = true;
                        if(audioInitialized) sounds.hide.triggerAttackRelease("C2", "8n");
                        document.getElementById('hiding-overlay').style.display = 'flex';
                        return;
                    }
                }
            }

            for (let i = collectibles.length - 1; i >= 0; i--) {
                const item = collectibles[i];
                if (player.position.distanceTo(item.position) < 1.5) {
                    if (item.name === "key") { if(audioInitialized) sounds.keyPickup.triggerAttackRelease("C5", "8n"); hasKey = true; showMessage('key-message'); } 
                    else if (item.name === "battery") { if(audioInitialized) sounds.batteryPickup.triggerAttackRelease("G5", "16n"); spareBatteries++; }
                    scene.remove(item); collectibles.splice(i, 1); return;
                }
            }
            if (door && player.position.distanceTo(door.position) < 2.5) {
                if (hasKey) { level++; loadLevel(level); } 
                else { if(audioInitialized) sounds.doorLocked.triggerAttackRelease("C2", "8n"); showMessage('door-locked-message'); }
            }
        }
        
        function showMessage(elementId, duration = 2000) { const msg = document.getElementById(elementId); msg.style.display = 'block'; setTimeout(() => { msg.style.display = 'none'; }, duration); }
        
        function updatePlayer(deltaTime) {
            if (isHiding) return;
            const isMoving = moveState.forward || moveState.backward || moveState.left || moveState.right;
            let currentSpeed = playerSpeed;

            if (moveState.sprint && isMoving && stamina > 0) {
                currentSpeed *= sprintMultiplier;
                stamina -= staminaDrainRate * deltaTime;
                staminaRegenCooldown = staminaRegenDelay;
            } else {
                if (staminaRegenCooldown > 0) { staminaRegenCooldown -= deltaTime; } 
                else if (stamina < maxStamina) { stamina += staminaRegenRate * deltaTime; }
            }
            stamina = Math.max(0, Math.min(maxStamina, stamina));
            
            const moveDirection = new THREE.Vector3((moveState.right?1:0)-(moveState.left?1:0),0,(moveState.backward?1:0)-(moveState.forward?1:0));
            moveDirection.normalize().applyQuaternion(player.quaternion);
            
            if (isMoving) { lastMoveDirection.copy(moveDirection); }

            playerVelocity.x = moveDirection.x * currentSpeed;
            playerVelocity.z = moveDirection.z * currentSpeed;

            const playerDelta = playerVelocity.clone().multiplyScalar(deltaTime);
            player.position.x += playerDelta.x; checkCollisions('x');
            player.position.z += playerDelta.z; checkCollisions('z');

            if (isMoving) {
                bobTime += deltaTime * (moveState.sprint ? 12 : 8);
                camera.position.y = 0.65 + Math.sin(bobTime) * 0.05;
                footstepCooldown -= deltaTime;
                if (audioInitialized && footstepCooldown <= 0) {
                    sounds.footstep.triggerAttackRelease("C1", "8n");
                    footstepCooldown = moveState.sprint ? 0.3 : 0.5;
                }
            } else { camera.position.y = 0.65; }
        }

        function checkCollisions(axis) {
            playerCollider.setFromCenterAndSize(player.position, playerSize);
            for (const box of collidableObjects) {
                if (!box.geometry.parameters.width || !box.geometry.parameters.height || !box.geometry.parameters.depth) continue;
                const boxCollider = new THREE.Box3().setFromObject(box);
                if (playerCollider.intersectsBox(boxCollider)) {
                    if (axis === 'x') {
                        if (playerVelocity.x > 0) player.position.x = boxCollider.min.x - playerSize.x / 2 - 0.001;
                        else if (playerVelocity.x < 0) player.position.x = boxCollider.max.x + playerSize.x / 2 + 0.001;
                    } else {
                        if (playerVelocity.z > 0) player.position.z = boxCollider.min.z - playerSize.z / 2 - 0.001;
                        else if (playerVelocity.z < 0) player.position.z = boxCollider.max.z + playerSize.z / 2 + 0.001;
                    }
                    playerCollider.setFromCenterAndSize(player.position, playerSize);
                }
            }
        }
        
        async function callGemini(prompt) {
            const apiKey = ""; 
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const payload = { contents: [{ parts: [{ text: prompt }] }] };
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload)
                });
                const result = await response.json();
                if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts[0]) {
                    return result.candidates[0].content.parts[0].text.replace(/["*]/g, '');
                }
            } catch (error) { console.error("Gemini API call failed:", error); return null; }
            return null;
        }

        async function updateWhispers(deltaTime) {
            whisperCooldown -= deltaTime; if (whisperCooldown > 0) return;
            whisperCooldown = 20.0 + Math.random() * 10;
            let prompt = null;
            const nearestEnemyDist = enemies.length > 0 ? player.position.distanceTo(enemies[0].model.position) : Infinity;
            if (flashlightBattery < 20) { prompt = "You are a monster in a dark maze. The player's flashlight is dying. Whisper a short, scary, one-sentence phrase in Turkish to taunt them about the coming darkness."; } 
            else if (stamina < 20) { prompt = "You are a monster in a dark maze. The player is exhausted and out of breath. Whisper a short, scary, one-sentence phrase in Turkish to taunt them about their fatigue."; } 
            else if (nearestEnemyDist < 5 && !isFlashlightOn) { prompt = "You are a monster in a dark maze, right behind the player who cannot see you. Whisper a short, scary, one-sentence phrase in Turkish.";}
            if (prompt) {
                const whisperText = await callGemini(prompt);
                if (whisperText) {
                    const whisperElement = document.getElementById('whisper-message');
                    whisperElement.innerText = whisperText; whisperElement.style.opacity = 1;
                    setTimeout(() => { whisperElement.style.opacity = 0; }, 4000);
                }
            }
        }

        function updateEnemies(deltaTime) {
            const flashlightPosition = flashlight.getWorldPosition(new THREE.Vector3());
            const flashlightDirection = new THREE.Vector3();
            camera.getWorldDirection(flashlightDirection);

            enemies.forEach((enemy) => {
                const model = enemy.model;
                if (!model) return; // Skip if model not loaded yet

                const distance = player.position.distanceTo(model.position);
                const vectorToEnemy = new THREE.Vector3().subVectors(model.position, flashlightPosition);
                
                const distanceToEnemy = vectorToEnemy.length();
                vectorToEnemy.normalize();
                const dotProduct = flashlightDirection.dot(vectorToEnemy);
                const angleToEnemy = Math.acos(dotProduct);
                const inFlashlightCone = angleToEnemy < flashlight.angle;

                let isLit = false;
                if (isFlashlightOn && inFlashlightCone && distanceToEnemy < flashlight.distance) {
                    raycaster.set(flashlightPosition, vectorToEnemy);
                    const intersects = raycaster.intersectObjects(collidableObjects, false);
                    if (intersects.length === 0 || intersects[0].distance > distanceToEnemy) {
                        isLit = true;
                    }
                }
                const directionToPlayer = new THREE.Vector3().subVectors(player.position, model.position).normalize();
                raycaster.set(model.position, directionToPlayer);
                const playerIntersects = raycaster.intersectObject(player);
                const wallIntersects = raycaster.intersectObjects(collidableObjects);
                
                if (playerIntersects.length > 0 && (wallIntersects.length === 0 || wallIntersects[0].distance > playerIntersects[0].distance)) {
                    enemy.aiState = 'CHASING';
                }

                if (isHiding) { enemy.aiState = 'PATROL'; }

                if (!isLit) {
                    if (enemy.aiState === 'CHASING') {
                        enemy.footstepCooldown -= deltaTime;
                        enemy.pathCooldown -= deltaTime;
                        if (enemy.pathCooldown <= 0) {
                            enemy.pathCooldown = 1.5;
                            const enemyGridPos = worldToGrid(model.position.x, model.position.z, mazeMap[0].length, mazeMap.length);
                            let targetGridPos = worldToGrid(player.position.x, player.position.z, mazeMap[0].length, mazeMap.length);
                            enemy.path = findPath(enemyGridPos, targetGridPos);
                            if (!enemy.path || enemy.path.length === 0) { enemy.aiState = 'PATROL'; }
                        }
                    } else if (enemy.aiState === 'PATROL') {
                        if (!enemy.path || enemy.path.length <= 1) {
                             const emptySpaces = [];
                             for (let i = 0; i < mazeMap.length; i++) { for (let j = 0; j < mazeMap[0].length; j++) { if (mazeMap[i][j] === 0) emptySpaces.push({ x: j, z: i }); } }
                             const randomTarget = emptySpaces[Math.floor(Math.random() * emptySpaces.length)];
                             const enemyGridPos = worldToGrid(model.position.x, model.position.z, mazeMap[0].length, mazeMap.length);
                             enemy.path = findPath(enemyGridPos, randomTarget);
                        }
                    }

                    if (enemy.path && enemy.path.length > 1) {
                        const targetGridPos = enemy.path[1];
                        const targetWorldPos = gridToWorld(targetGridPos.x, targetGridPos.z, mazeMap[0].length, mazeMap.length);
                        targetWorldPos.y = model.position.y;
                        const directionToTarget = new THREE.Vector3().subVectors(targetWorldPos, model.position).normalize();
                        model.position.add(directionToTarget.multiplyScalar(enemy.speed * deltaTime));
                        model.lookAt(targetWorldPos);

                        if(audioInitialized && enemy.footstepCooldown <= 0) {
                            const volume = Math.max(-30, 0 - distance * 1.5);
                            sounds.enemyFootstep.volume.value = volume;
                            sounds.enemyFootstep.triggerAttackRelease("C1", "8n");
                            enemy.footstepCooldown = 0.7;
                        }

                        if(model.position.distanceTo(targetWorldPos) < 0.5) { enemy.path.shift(); }
                    }
                } else {
                    if (distance < 1.8) { 
                        const pushDirection = new THREE.Vector3().subVectors(model.position, player.position).normalize();
                        pushDirection.y = 0; 
                        model.position.add(pushDirection.multiplyScalar(1.5 * deltaTime)); 
                    }
                }
                
                if (distance < 1.0) { gameOver(); }
            });
        }
        
        function updateFlashlight(deltaTime) {
            if (isFlashlightOn && flashlightBattery > 0) {
                flashlightBattery -= flashlightDrainRate * deltaTime;
            }
            if (flashlightBattery <= 0) {
                flashlightBattery = 0;
                isFlashlightOn = false;
                flashlight.visible = false;
            }
            flashlight.intensity = baseFlashlightIntensity * (flashlightBattery / maxFlashlightBattery);
        }
        
        function gameOver() {
            if (!isGameOver) {
                isGameOver = true;
                if (audioInitialized) sounds.gameOver.triggerAttackRelease("4n");
                gameActive = false;
                document.getElementById('game-over-taunt').innerText = "Kaçış yoktu.";
                document.exitPointerLock();
            }
        }

        function drawMinimap() {
            if (!gameActive || !mazeMap) return;
            minimapCtx.clearRect(0, 0, minimapCanvas.width, minimapCanvas.height);
            
            const playerGridPos = worldToGrid(player.position.x, player.position.z, mazeMap[0].length, mazeMap.length);
            
            const revealRadius = 1;
            for(let z = -revealRadius; z <= revealRadius; z++) {
                for(let x = -revealRadius; x <= revealRadius; x++) {
                    const checkX = playerGridPos.x + x;
                    const checkZ = playerGridPos.z + z;
                    if(mazeMap[checkZ] && mazeMap[checkZ][checkX] !== undefined) {
                       visitedMap[checkZ][checkX] = true;
                    }
                }
            }

            for (let z = 0; z < mazeMap.length; z++) {
                for (let x = 0; x < mazeMap[0].length; x++) {
                    if (!visitedMap[z][x]) continue;

                    if (mazeMap[z][x] === 1) { minimapCtx.fillStyle = '#666'; } 
                    else { minimapCtx.fillStyle = '#222'; }
                    minimapCtx.fillRect(x * minimapCellSize, z * minimapCellSize, minimapCellSize, minimapCellSize);

                    if (mazeMap[z][x] === 2) {
                         minimapCtx.fillStyle = '#5c3e1a';
                         minimapCtx.fillRect(x * minimapCellSize, z * minimapCellSize, minimapCellSize, minimapCellSize);
                    }
                }
            }

            for(const item of collectibles) {
                const itemGridPos = worldToGrid(item.position.x, item.position.z, mazeMap[0].length, mazeMap.length);
                if(visitedMap[itemGridPos.z][itemGridPos.x]) {
                    minimapCtx.fillStyle = item.name === 'key' ? '#ffd700' : '#33ff55';
                    minimapCtx.fillRect(itemGridPos.x * minimapCellSize + 2, itemGridPos.z * minimapCellSize + 2, minimapCellSize - 4, minimapCellSize - 4);
                }
            }
            for(const item of interactables) {
                const itemGridPos = worldToGrid(item.position.x, item.position.z, mazeMap[0].length, mazeMap.length);
                if(visitedMap[itemGridPos.z][itemGridPos.x]) {
                    minimapCtx.fillStyle = '#403020';
                    minimapCtx.fillRect(itemGridPos.x * minimapCellSize + 2, itemGridPos.z * minimapCellSize + 2, minimapCellSize - 4, minimapCellSize - 4);
                }
            }

            minimapCtx.fillStyle = '#fff';
            minimapCtx.fillRect(playerGridPos.x * minimapCellSize, playerGridPos.z * minimapCellSize, minimapCellSize, minimapCellSize);
        }

        function animate() {
            requestAnimationFrame(animate);
            const deltaTime = Math.min(0.05, clock.getDelta());
            
            document.getElementById('stamina-bar').style.width = (stamina / maxStamina) * 100 + '%';
            document.getElementById('battery-bar').style.width = (flashlightBattery / maxFlashlightBattery) * 100 + '%';
            document.getElementById('spare-batteries-ui').innerText = `🔋 ${spareBatteries}`;

            if (gameActive) {
                updatePlayer(deltaTime);
                updateEnemies(deltaTime);
                updateFlashlight(deltaTime);
                updateWhispers(deltaTime);
                drawMinimap();
            }
            renderer.render(scene, camera);
        }

        init();
    </script>
</body>
</html>
